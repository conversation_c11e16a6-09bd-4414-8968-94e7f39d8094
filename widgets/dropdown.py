from fabric.widgets.centerbox import <PERSON>Box
from fabric.widgets.box import Box
from fabric.widgets.eventbox import Event<PERSON>ox
from widgets.wayland import WaylandWindow as Window
from widgets.popup_window import PopupWindow
from gi.repository import GtkLayerShell, GLib
import cairo

from utils.service import modus_service

dropdowns = []


def dropdown_divider(comment=None):
    return Box(
        children=[Box(name="dropdown-divider", h_expand=True)],
        name="dropdown-divider-box",
        h_align="fill",
        h_expand=True,
        v_expand=True,
    )


class DropdownOverlay(Window):
    """A simple overlay that uses pass-through with selective event capture"""

    def __init__(self, dropdown_window, **kwargs):
        super().__init__(
            layer="overlay",  # Use overlay layer
            anchor="top bottom left right",
            exclusivity="auto",
            title="dropdown-overlay",
            name="DropdownOverlay",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.dropdown_window = dropdown_window
        GtkLayerShell.set_exclusive_zone(self, -1)

        # Start with pass-through enabled
        self.pass_through = True

        # Create transparent event box
        self.event_box = EventBox(
            events=["button-press-event"],
            all_visible=True,
        )

        self.event_box.connect("button-press-event", self.on_overlay_click)
        self.children = [self.event_box]

        # Make the overlay transparent
        self.set_app_paintable(True)
        self.connect("draw", self.on_draw)

    def on_draw(self, _widget, cr):
        """Make overlay transparent"""
        cr.set_source_rgba(0, 0, 0, 0)  # Fully transparent
        cr.set_operator(cairo.OPERATOR_SOURCE)
        cr.paint()
        return False

    def on_overlay_click(self, _widget, event):
        """Handle overlay clicks - this should only happen for outside clicks"""
        if not self.dropdown_window.is_visible():
            return False

        # Hide dropdown with delay to allow button actions
        GLib.timeout_add(100, lambda: self.hide_dropdown() or False)
        return True

    def hide_dropdown(self):
        """Hide the dropdown and overlay"""
        self.dropdown_window.hide()
        self.hide()
        if modus_service.current_dropdown == self.dropdown_window.id:
            modus_service.current_dropdown = None
        return False

    def show_with_dropdown(self):
        """Show overlay when dropdown is shown"""
        # Temporarily disable pass-through to capture outside clicks
        self.pass_through = False
        self.show()

        # Re-enable pass-through after a short delay to allow dropdown to be clickable
        GLib.timeout_add(100, self._enable_selective_passthrough)

    def _enable_selective_passthrough(self):
        """Enable pass-through for dropdown area only"""
        if not self.dropdown_window.is_visible():
            return False

        try:
            # Create input region that excludes dropdown area
            from gi.repository import Gdk

            # Get screen dimensions
            screen = Gdk.Screen.get_default()
            screen_width = screen.get_width()
            screen_height = screen.get_height()

            # Get dropdown bounds
            dropdown_x, dropdown_y = self.dropdown_window.get_position()
            dropdown_allocation = self.dropdown_window.get_allocation()

            # Create region covering entire screen
            full_region = cairo.Region(cairo.RectangleInt(0, 0, screen_width, screen_height))

            # Create region for dropdown area
            dropdown_rect = cairo.RectangleInt(
                max(0, int(dropdown_x)),
                max(0, int(dropdown_y)),
                min(screen_width, dropdown_allocation.width),
                min(screen_height, dropdown_allocation.height)
            )
            dropdown_region = cairo.Region(dropdown_rect)

            # Subtract dropdown region from full region
            full_region.subtract(dropdown_region)

            # Set input shape to only capture clicks outside dropdown
            self.input_shape_combine_region(full_region)

        except Exception as e:
            print(f"Error setting input region: {e}")
            # Fallback: use pass-through
            self.pass_through = True

        return False

    def hide_with_dropdown(self):
        """Hide overlay when dropdown is hidden"""
        self.pass_through = True
        self.hide()


class ModusDropdown(PopupWindow):
    """A Dropdown for Modusshell with overlay-based click detection"""

    def __init__(self, dropdown_children=None, dropdown_id=None, **kwargs):
        super().__init__(
            layer="top",
            exclusivity="auto",
            name="dropdown-menu",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.id = dropdown_id or str(len(dropdowns))
        dropdowns.append(self)

        # Create overlay for detecting outside clicks
        self.overlay = DropdownOverlay(dropdown_window=self)

        modus_service.connect("dropdowns-hide-changed", self.hide_dropdown)

        self.dropdown = Box(
            children=dropdown_children or [],
            h_expand=True,
            name="dropdown-options",
            orientation="vertical",
        )

        self.child_box = CenterBox(start_children=[self.dropdown])

        self.event_box = EventBox(
            events=["enter-notify-event", "leave-notify-event"],
            child=self.child_box,
            all_visible=True,
        )

        self.children = [self.event_box]
        self.event_box.connect("enter-notify-event", self.on_cursor_enter)
        self.event_box.connect("leave-notify-event", self.on_cursor_leave)
        # Remove direct button-press-event to avoid blocking button actions
        self.add_keybinding("Escape", self.hide_dropdown)

    def toggle_dropdown(self, *args, **kwargs):
        """Toggle dropdown visibility with overlay"""
        if self.is_visible():
            self.hide()
            self.overlay.hide_with_dropdown()
        else:
            self.show()
            self.overlay.show_with_dropdown()
            modus_service.current_dropdown = self.id

    def hide_dropdown(self, *args, **kwargs):
        """Hide dropdown if it's not the current active dropdown"""
        if modus_service.current_dropdown != self.id:
            self.hide()
            self.overlay.hide_with_dropdown()

    def hide(self):
        """Override hide to also hide overlay"""
        super().hide()
        self.overlay.hide_with_dropdown()
        if modus_service.current_dropdown == self.id:
            modus_service.current_dropdown = None

    def show(self):
        """Override show to also show overlay"""
        super().show()
        modus_service.current_dropdown = self.id

    def on_cursor_enter(self, *_):
        """Handle cursor entering dropdown area"""
        # Keep dropdown visible when cursor enters
        pass

    def on_cursor_leave(self, *_):
        """Handle cursor leaving dropdown area"""
        if self.is_hovered():
            return
        # Use a small delay to allow button clicks to complete
        GLib.timeout_add(200, self._delayed_hide)

    def _delayed_hide(self):
        """Delayed hide to allow button actions to complete"""
        if not self.is_hovered():
            self.hide()
            self.overlay.hide_with_dropdown()
        return False  # Don't repeat the timeout
