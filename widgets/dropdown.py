from fabric.widgets.centerbox import Center<PERSON>ox
from fabric.widgets.box import Box
from fabric.widgets.eventbox import Event<PERSON>ox
from widgets.wayland import Wayland<PERSON>indow as Window
from widgets.popup_window import PopupWindow
from gi.repository import GtkLayerShell, GLib
import cairo

from utils.service import modus_service

dropdowns = []


def dropdown_divider(comment=None):
    return Box(
        children=[Box(name="dropdown-divider", h_expand=True)],
        name="dropdown-divider-box",
        h_align="fill",
        h_expand=True,
        v_expand=True,
    )


class DropdownOverlay(Window):
    """A background overlay that captures outside clicks without blocking dropdown"""

    def __init__(self, dropdown_window, **kwargs):
        super().__init__(
            layer="top",  # Use top layer to capture events
            anchor="top bottom left right",
            exclusivity="auto",
            title="dropdown-overlay",
            name="DropdownOverlay",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.dropdown_window = dropdown_window
        GtkLayerShell.set_exclusive_zone(self, -1)

        # Create transparent event box that captures all clicks
        self.event_box = EventBox(
            events=["button-press-event"],
            all_visible=True,
        )

        self.event_box.connect("button-press-event", self.on_overlay_click)
        self.children = [self.event_box]

        # Make the overlay transparent
        self.set_app_paintable(True)
        self.connect("draw", self.on_draw)

    def on_draw(self, _widget, cr):
        """Make overlay transparent"""
        cr.set_source_rgba(0, 0, 0, 0)  # Fully transparent
        cr.set_operator(cairo.OPERATOR_SOURCE)
        cr.paint()
        return False

    def on_overlay_click(self, _widget, event):
        """Handle overlay clicks - check if click is outside dropdown"""
        if not self.dropdown_window.is_visible():
            return False

        # Get click coordinates
        click_x = event.x_root
        click_y = event.y_root

        # Get dropdown window bounds
        try:
            dropdown_x, dropdown_y = self.dropdown_window.get_position()
            dropdown_allocation = self.dropdown_window.get_allocation()

            # Check if click is inside dropdown bounds
            inside_dropdown = (
                dropdown_x <= click_x <= dropdown_x + dropdown_allocation.width and
                dropdown_y <= click_y <= dropdown_y + dropdown_allocation.height
            )

            if not inside_dropdown:
                # Click is outside dropdown - hide it
                GLib.timeout_add(50, lambda: self.hide_dropdown() or False)
                return True  # Consume the event

        except Exception as e:
            print(f"Error checking click position: {e}")
            # If we can't determine position, hide dropdown to be safe
            GLib.timeout_add(50, lambda: self.hide_dropdown() or False)
            return True

        # Click is inside dropdown - don't consume event
        return False

    def hide_dropdown(self):
        """Hide the dropdown and overlay"""
        self.dropdown_window.hide()
        self.hide()
        if modus_service.current_dropdown == self.dropdown_window.id:
            modus_service.current_dropdown = None
        return False

    def show_with_dropdown(self):
        """Show overlay when dropdown is shown"""
        self.show()

    def hide_with_dropdown(self):
        """Hide overlay when dropdown is hidden"""
        self.hide()


class ModusDropdown(PopupWindow):
    """A Dropdown for Modusshell with overlay-based click detection"""

    def __init__(self, dropdown_children=None, dropdown_id=None, **kwargs):
        super().__init__(
            layer="overlay",  # Use overlay layer to be above the background overlay
            exclusivity="auto",
            name="dropdown-menu",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.id = dropdown_id or str(len(dropdowns))
        dropdowns.append(self)

        # Create overlay for detecting outside clicks
        self.overlay = DropdownOverlay(dropdown_window=self)

        modus_service.connect("dropdowns-hide-changed", self.hide_dropdown)

        self.dropdown = Box(
            children=dropdown_children or [],
            h_expand=True,
            name="dropdown-options",
            orientation="vertical",
        )

        self.child_box = CenterBox(start_children=[self.dropdown])

        self.event_box = EventBox(
            events=["enter-notify-event", "leave-notify-event"],
            child=self.child_box,
            all_visible=True,
        )

        self.children = [self.event_box]
        self.event_box.connect("enter-notify-event", self.on_cursor_enter)
        self.event_box.connect("leave-notify-event", self.on_cursor_leave)
        # Remove direct button-press-event to avoid blocking button actions
        self.add_keybinding("Escape", self.hide_dropdown)

    def toggle_dropdown(self, *args, **kwargs):
        """Toggle dropdown visibility with overlay"""
        if self.is_visible():
            self.hide()
            self.overlay.hide_with_dropdown()
        else:
            self.show()
            self.overlay.show_with_dropdown()
            modus_service.current_dropdown = self.id

    def hide_dropdown(self, *args, **kwargs):
        """Hide dropdown if it's not the current active dropdown"""
        if modus_service.current_dropdown != self.id:
            self.hide()
            self.overlay.hide_with_dropdown()

    def hide(self):
        """Override hide to also hide overlay"""
        super().hide()
        self.overlay.hide_with_dropdown()
        if modus_service.current_dropdown == self.id:
            modus_service.current_dropdown = None

    def show(self):
        """Override show to also show overlay"""
        super().show()
        modus_service.current_dropdown = self.id

    def on_cursor_enter(self, *_):
        """Handle cursor entering dropdown area"""
        # Keep dropdown visible when cursor enters
        pass

    def on_cursor_leave(self, *_):
        """Handle cursor leaving dropdown area"""
        if self.is_hovered():
            return
        # Use a small delay to allow button clicks to complete
        GLib.timeout_add(200, self._delayed_hide)

    def _delayed_hide(self):
        """Delayed hide to allow button actions to complete"""
        if not self.is_hovered():
            self.hide()
            self.overlay.hide_with_dropdown()
        return False  # Don't repeat the timeout
