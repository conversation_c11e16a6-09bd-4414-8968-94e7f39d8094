from fabric.widgets.centerbox import CenterBox
from fabric.widgets.box import Box
from fabric.widgets.eventbox import EventBox
from widgets.popup_window import PopupWindow

from utils.service import modus_service

dropdowns = []


def dropdown_divider(*args, **kwargs):
    """Create a dropdown divider element"""
    return Box(
        children=[Box(name="dropdown-divider", h_expand=True)],
        name="dropdown-divider-box",
        h_align="fill",
        h_expand=True,
        v_expand=True,
    )


class ModusDropdown(PopupWindow):
    """A Dropdown for Modusshell that doesn't interfere with button actions"""

    def __init__(self, dropdown_children=None, dropdown_id=None, **kwargs):
        super().__init__(
            layer="top",
            exclusivity="auto",
            name="dropdown-menu",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.id = dropdown_id or str(len(dropdowns))
        dropdowns.append(self)

        # Track if we're in the middle of a button action
        self._button_action_in_progress = False
        self._hide_timeout_id = None

        modus_service.connect("dropdowns-hide-changed", self.hide_dropdown)

        self.dropdown = Box(
            children=dropdown_children or [],
            h_expand=True,
            name="dropdown-options",
            orientation="vertical",
        )

        self.child_box = CenterBox(start_children=[self.dropdown])

        self.event_box = EventBox(
            events=["enter-notify-event", "leave-notify-event"],
            child=self.child_box,
            all_visible=True,
        )

        self.children = [self.event_box]
        self.event_box.connect("enter-notify-event", self.on_cursor_enter)
        self.event_box.connect("leave-notify-event", self.on_cursor_leave)
        self.add_keybinding("Escape", self.hide_dropdown)

        # Set up focus-out detection for hiding dropdown
        self.connect("focus-out-event", self.on_focus_out)

    def toggle_dropdown(self, *args, **kwargs):
        """Toggle dropdown visibility"""
        if self.is_visible():
            self.hide()
        else:
            self.show()
            modus_service.current_dropdown = self.id

    def hide_dropdown(self, *args, **kwargs):
        """Hide dropdown if it's not the current active dropdown"""
        if modus_service.current_dropdown != self.id:
            self.hide()

    def on_focus_out(self, widget, event):
        """Handle focus out events - hide dropdown when focus is lost"""
        # Small delay to allow button actions to complete
        from gi.repository import GLib
        if self._hide_timeout_id:
            GLib.source_remove(self._hide_timeout_id)
        self._hide_timeout_id = GLib.timeout_add(200, self._delayed_hide_on_focus_out)
        return False

    def on_cursor_enter(self, *_):
        """Handle cursor entering dropdown area"""
        # Cancel any pending hide timeout when cursor enters
        if self._hide_timeout_id:
            from gi.repository import GLib
            GLib.source_remove(self._hide_timeout_id)
            self._hide_timeout_id = None

    def on_cursor_leave(self, *_):
        """Handle cursor leaving dropdown area"""
        if self.is_hovered():
            return

        # Use a delay to allow button clicks to complete
        from gi.repository import GLib
        if self._hide_timeout_id:
            GLib.source_remove(self._hide_timeout_id)
        self._hide_timeout_id = GLib.timeout_add(300, self._delayed_hide)

    def _delayed_hide(self):
        """Delayed hide to allow button actions to complete"""
        self._hide_timeout_id = None
        if not self.is_hovered():
            self.hide()
        return False  # Don't repeat the timeout

    def _delayed_hide_on_focus_out(self):
        """Delayed hide on focus out"""
        self._hide_timeout_id = None
        if not self.is_hovered():
            self.hide()
        return False  # Don't repeat the timeout

    def hide(self):
        """Override hide to clean up state"""
        if self._hide_timeout_id:
            from gi.repository import GLib
            GLib.source_remove(self._hide_timeout_id)
            self._hide_timeout_id = None

        super().hide()
        if modus_service.current_dropdown == self.id:
            modus_service.current_dropdown = None

    def show(self):
        """Override show to set up state"""
        super().show()
        modus_service.current_dropdown = self.id
