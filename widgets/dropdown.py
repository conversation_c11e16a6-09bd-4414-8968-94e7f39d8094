from fabric.widgets.centerbox import CenterBox
from fabric.widgets.box import Box
from fabric.widgets.eventbox import EventBox
from widgets.popup_window import PopupWindow
from widgets.mousecapture import DropDownMouseCapture

from utils.service import modus_service

dropdowns = []


def dropdown_divider(*args, **kwargs):
    """Create a dropdown divider element"""
    return Box(
        children=[Box(name="dropdown-divider", h_expand=True)],
        name="dropdown-divider-box",
        h_align="fill",
        h_expand=True,
        v_expand=True,
    )


class ModusDropdown(PopupWindow):
    """A Dropdown for Modusshell that doesn't interfere with button actions"""

    def __init__(self, dropdown_children=None, dropdown_id=None, **kwargs):
        super().__init__(
            layer="top",
            exclusivity="auto",
            name="dropdown-menu",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.id = dropdown_id or str(len(dropdowns))
        dropdowns.append(self)

        # Create mouse capture for handling clicks outside dropdown
        self.mouse_capture = DropDownMouseCapture(
            layer="top",
            child_window=self,
        )

        modus_service.connect("dropdowns-hide-changed", self.hide_dropdown)

        self.dropdown = Box(
            children=dropdown_children or [],
            h_expand=True,
            name="dropdown-options",
            orientation="vertical",
        )

        self.child_box = CenterBox(start_children=[self.dropdown])

        self.event_box = EventBox(
            events=["enter-notify-event", "leave-notify-event"],
            child=self.child_box,
            all_visible=True,
        )

        self.children = [self.event_box]
        self.event_box.connect("enter-notify-event", self.on_cursor_enter)
        self.event_box.connect("leave-notify-event", self.on_cursor_leave)
        self.add_keybinding("Escape", self.hide_dropdown)

    def _init_mousecapture(self, mouse_capture: DropDownMouseCapture) -> None:
        """Initialize mouse capture - called by MouseCapture"""
        self.mouse_capture = mouse_capture

    def _set_mousecapture(self, visible: bool) -> None:
        """Set mouse capture visibility - called by MouseCapture"""
        if visible:
            self.show()
            modus_service.current_dropdown = self.id
        else:
            self.hide()
            if modus_service.current_dropdown == self.id:
                modus_service.current_dropdown = None

    def toggle_dropdown(self, *args, **kwargs):
        """Toggle dropdown visibility with mouse capture"""
        if self.is_visible():
            self.mouse_capture.set_child_window_visible(False)
        else:
            self.mouse_capture.set_child_window_visible(True)

    def hide_dropdown(self, *args, **kwargs):
        """Hide dropdown if it's not the current active dropdown"""
        if modus_service.current_dropdown != self.id:
            self.mouse_capture.set_child_window_visible(False)

    def on_cursor_enter(self, *_):
        """Handle cursor entering dropdown area"""
        # Don't auto-show on enter to prevent interference with button actions
        pass

    def on_cursor_leave(self, *_):
        """Handle cursor leaving dropdown area"""
        if self.is_hovered():
            return
        # Use a small delay to allow button clicks to complete
        from gi.repository import GLib
        GLib.timeout_add(100, self._delayed_hide)

    def _delayed_hide(self):
        """Delayed hide to allow button actions to complete"""
        if not self.is_hovered():
            self.mouse_capture.set_child_window_visible(False)
        return False  # Don't repeat the timeout
